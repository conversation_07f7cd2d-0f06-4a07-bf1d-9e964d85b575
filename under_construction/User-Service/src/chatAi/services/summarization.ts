import { OpenRouterService } from './openrouter';

export interface DocumentSummary {
  main_summary: string;
  quick_summary: string; // Concise 1-2 sentence overview for Chat AI tab
  key_points: string[];
  topics: string[];
  entities: string[];
  suggested_questions: string[];
  document_type: string;
  reading_time_minutes: number;
  complexity_level: 'beginner' | 'intermediate' | 'advanced';
}

export class SummarizationService {
  private openRouterService: OpenRouterService;

  constructor() {
    this.openRouterService = new OpenRouterService();
  }

  /**
   * Generate comprehensive summary and insights for a document
   */
  async generateDocumentSummary(
    documentText: string, 
    filename: string, 
    pageCount: number, 
    wordCount: number
  ): Promise<DocumentSummary> {
    try {
      // Create a structured prompt for comprehensive analysis
      const prompt = this.buildSummarizationPrompt(documentText, filename, pageCount, wordCount);
      
      // Generate summary using OpenRouter
      const summaryResponse = await this.openRouterService.generateResponse(prompt, "");
      
      // Parse the structured response
      const summary = this.parseSummaryResponse(summaryResponse, wordCount);
      
      return summary;
    } catch (error) {
      console.error('Summarization error:', error);
      // Return fallback summary
      return this.generateFallbackSummary(documentText, filename, wordCount);
    }
  }

  /**
   * Build a comprehensive summarization prompt
   */
  private buildSummarizationPrompt(text: string, filename: string, pageCount: number, wordCount: number): string {
    // Truncate text if too long (keep first 8000 chars for context)
    const truncatedText = text.length > 8000 ? text.substring(0, 8000) + "..." : text;
    
    return `Analyze the following document and provide a comprehensive summary in JSON format.

Document Details:
- Filename: ${filename}
- Pages: ${pageCount}
- Word Count: ${wordCount}

Document Content:
${truncatedText}

Please provide a JSON response with the following structure:
{
  "main_summary": "A comprehensive 3-4 sentence summary of the document",
  "quick_summary": "A concise 1-2 sentence overview perfect for a chat interface",
  "key_points": ["5-7 most important points from the document"],
  "topics": ["main topics/themes covered"],
  "entities": ["important people, organizations, dates, numbers mentioned"],
  "suggested_questions": ["5 good questions someone might ask about this document"],
  "document_type": "identify the type: report, research paper, manual, contract, etc.",
  "complexity_level": "beginner, intermediate, or advanced"
}

Important: Respond ONLY with valid JSON. No additional text or explanation.`;
  }

  /**
   * Parse the AI response into a structured summary
   */
  private parseSummaryResponse(response: string, wordCount: number): DocumentSummary {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? jsonMatch[0] : response;
      
      const parsed = JSON.parse(jsonStr);
      
      // Validate and normalize the response
      return {
        main_summary: parsed.main_summary || "Document summary unavailable",
        quick_summary: parsed.quick_summary || parsed.main_summary || "Quick analysis of the document is available.",
        key_points: Array.isArray(parsed.key_points) ? parsed.key_points.slice(0, 7) : [],
        topics: Array.isArray(parsed.topics) ? parsed.topics.slice(0, 5) : [],
        entities: Array.isArray(parsed.entities) ? parsed.entities.slice(0, 8) : [],
        suggested_questions: Array.isArray(parsed.suggested_questions) ? parsed.suggested_questions.slice(0, 5) : [],
        document_type: parsed.document_type || "document",
        reading_time_minutes: Math.max(1, Math.ceil(wordCount / 200)), // ~200 words per minute
        complexity_level: ['beginner', 'intermediate', 'advanced'].includes(parsed.complexity_level) 
          ? parsed.complexity_level 
          : 'intermediate'
      };
    } catch (error) {
      console.error('Failed to parse summary response:', error);
      // Return basic analysis
      return this.generateBasicSummary(response, wordCount);
    }
  }

  /**
   * Generate a basic summary from the raw response
   */
  private generateBasicSummary(response: string, wordCount: number): DocumentSummary {
    const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    return {
      main_summary: sentences.slice(0, 3).join('. ').trim() + '.',
      quick_summary: sentences.slice(0, 1).join('. ').trim() + '.',
      key_points: sentences.slice(0, 5).map(s => s.trim()).filter(s => s.length > 10),
      topics: [], // Will be empty for basic summary
      entities: [],
      suggested_questions: [
        "What is this document about?",
        "What are the main points?",
        "Can you explain this in more detail?"
      ],
      document_type: "document",
      reading_time_minutes: Math.max(1, Math.ceil(wordCount / 200)),
      complexity_level: 'intermediate'
    };
  }

  /**
   * Generate a fallback summary when AI fails
   */
  private generateFallbackSummary(text: string, filename: string, wordCount: number): DocumentSummary {
    const fileExtension = filename.split('.').pop()?.toLowerCase();
    const docType = this.inferDocumentType(fileExtension, text);
    
    return {
      main_summary: `This ${docType} contains ${wordCount} words and covers various topics. The document has been processed and is ready for AI-powered questions and analysis.`,
      quick_summary: `${docType.charAt(0).toUpperCase() + docType.slice(1)} with ${wordCount} words, ready for analysis.`,
      key_points: [
        `Document contains ${wordCount} words`,
        "Content has been processed and indexed",
        "Ready for AI-powered analysis"
      ],
      topics: [],
      entities: [],
      suggested_questions: [
        "What is this document about?",
        "Can you summarize the main points?",
        "What are the key topics covered?",
        "Are there any important dates or numbers?",
        "What questions can I ask about this document?"
      ],
      document_type: docType,
      reading_time_minutes: Math.max(1, Math.ceil(wordCount / 200)),
      complexity_level: 'intermediate'
    };
  }

  /**
   * Infer document type from file extension and content
   */
  private inferDocumentType(extension?: string, text?: string): string {
    if (!extension) return "document";
    
    const typeMap: { [key: string]: string } = {
      'pdf': 'PDF document',
      'docx': 'Word document', 
      'doc': 'Word document',
      'txt': 'text file',
      'pptx': 'PowerPoint presentation',
      'ppt': 'PowerPoint presentation',
      'xlsx': 'Excel spreadsheet',
      'xls': 'Excel spreadsheet',
      'csv': 'CSV data file',
      'md': 'Markdown document',
      'html': 'web page',
      'htm': 'web page'
    };
    
    return typeMap[extension] || "document";
  }

  /**
   * Check if a document should be summarized (only for ready documents)
   */
  shouldSummarize(status: string, wordCount?: number): boolean {
    return status === 'ready' && (wordCount || 0) > 50; // Don't summarize very short documents
  }
}

export const summarizationService = new SummarizationService();
